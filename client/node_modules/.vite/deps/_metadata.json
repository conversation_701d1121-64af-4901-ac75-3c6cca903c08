{"hash": "450235da", "configHash": "937e3d8d", "lockfileHash": "1a41009e", "browserHash": "a8af0cb8", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "0cb432eb", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "34eead0d", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "95adb7c7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3006e559", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "573e99b5", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "701e2e68", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "6f936288", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2d1f1127", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "86c67189", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "726ed8ed", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "a3e062d0", "needsInterop": false}, "hono/client": {"src": "../../../../server/node_modules/hono/dist/client/index.js", "file": "hono_client.js", "fileHash": "c2ee9a7f", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4900bf94", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6c245e79", "needsInterop": true}, "recharts": {"src": "../../../../node_modules/recharts/es6/index.js", "file": "recharts.js", "fileHash": "a8bd7059", "needsInterop": false}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "05768362", "needsInterop": false}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "f558d67a", "needsInterop": false}}, "chunks": {"chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-LTSSTYZQ": {"file": "chunk-LTSSTYZQ.js"}, "chunk-64JE5CLV": {"file": "chunk-64JE5CLV.js"}, "chunk-DWITMLRW": {"file": "chunk-DWITMLRW.js"}, "chunk-OJSHQKZH": {"file": "chunk-OJSHQKZH.js"}, "chunk-UAZVLAD2": {"file": "chunk-UAZVLAD2.js"}, "chunk-BFOWCZE5": {"file": "chunk-BFOWCZE5.js"}, "chunk-KLBMN725": {"file": "chunk-KLBMN725.js"}, "chunk-6P7CGRIP": {"file": "chunk-6P7CGRIP.js"}, "chunk-PSS62N5V": {"file": "chunk-PSS62N5V.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}