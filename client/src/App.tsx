import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Dashboard } from './components/dashboard/Dashboard';
import { ManajemenPengunjung } from './components/dashboard/ManajemenPengunjung';
import { ManajemenPengguna } from './components/dashboard/ManajemenPengguna';
import { ManajemenKonten } from './components/dashboard/ManajemenKonten';
import { RoleManagement } from './components/dashboard/RoleManagement';
import { BackupRestore } from './components/dashboard/BackupRestore';
import { Logs } from './components/dashboard/Logs';
import { DashboardLayout } from './components/dashboard/DashboardLayout';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        } />
        <Route path="/manajemen-pengunjung" element={
          <DashboardLayout>
            <ManajemenPengunjung />
          </DashboardLayout>
        } />
        <Route path="/pengaturan-sistem" element={
          <DashboardLayout>
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900">Pengaturan Sistem</h2>
              <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
            </div>
          </DashboardLayout>
        } />
        <Route path="/manajemen-pengguna" element={
          <DashboardLayout>
            <ManajemenPengguna />
          </DashboardLayout>
        } />
        <Route path="/manajemen-konten" element={
          <DashboardLayout>
            <ManajemenKonten />
          </DashboardLayout>
        } />
        <Route path="/role-management" element={
          <DashboardLayout>
            <RoleManagement />
          </DashboardLayout>
        } />
        <Route path="/backup-restore" element={
          <DashboardLayout>
            <BackupRestore />
          </DashboardLayout>
        } />
        <Route path="/logs" element={
          <DashboardLayout>
            <Logs />
          </DashboardLayout>
        } />
      </Routes>
    </Router>
  );
}

export default App