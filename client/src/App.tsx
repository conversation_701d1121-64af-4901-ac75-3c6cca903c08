import { useState } from 'react';
import { Dashboard } from './components/dashboard/Dashboard';
import { ManajemenPengunjung } from './components/dashboard/ManajemenPengunjung';
import { DashboardLayout } from './components/dashboard/DashboardLayout';

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'manajemen-pengunjung':
        return (
          <DashboardLayout currentPage={currentPage} onNavigate={setCurrentPage}>
            <ManajemenPengunjung />
          </DashboardLayout>
        );
      case 'pengaturan-sistem':
        return (
          <DashboardLayout currentPage={currentPage} onNavigate={setCurrentPage}>
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900">Pengaturan Sistem</h2>
              <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
            </div>
          </DashboardLayout>
        );
      case 'backup-restore':
        return (
          <DashboardLayout currentPage={currentPage} onNavigate={setCurrentPage}>
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900">Backup & Restore</h2>
              <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
            </div>
          </DashboardLayout>
        );
      case 'logs':
        return (
          <DashboardLayout currentPage={currentPage} onNavigate={setCurrentPage}>
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-900">Logs</h2>
              <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
            </div>
          </DashboardLayout>
        );
      default:
        return <Dashboard />;
    }
  };

  if (currentPage === 'dashboard') {
    return (
      <DashboardLayout currentPage={currentPage} onNavigate={setCurrentPage}>
        <Dashboard />
      </DashboardLayout>
    );
  }

  return renderPage();
}

export default App