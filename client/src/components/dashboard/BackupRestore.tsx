import { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { Download, RotateCcw, Trash2 } from 'lucide-react';

interface BackupLog {
  id: string;
  date: string;
  type: 'Auto-Backup' | 'Manual';
  status: 'Berhasil' | 'Gagal';
  size: string;
}

export function BackupRestore() {
  const [backupSchedule, setBackupSchedule] = useState('daily');
  const [backupLogs] = useState<BackupLog[]>([
    {
      id: '1',
      date: '2023-10-01',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.5 GB'
    },
    {
      id: '2',
      date: '2023-09-24',
      type: 'Manual',
      status: 'Gagal',
      size: '1.2 GB'
    },
    {
      id: '3',
      date: '2023-09-15',
      type: 'Auto-Backup',
      status: 'Berhasil',
      size: '1.3 GB'
    }
  ]);

  const getStatusBadge = (status: string) => {
    return status === 'Berhasil' 
      ? <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Berhasil</Badge>
      : <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Gagal</Badge>;
  };

  const BackupContent = () => (
    <div className="space-y-6">
      {/* Auto Backup Schedule & Export/Import */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Jadwal Auto-Backup */}
        <Card className="bg-white border border-gray-200">
          <CardContent className="p-4 lg:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Jadwal Auto-Backup</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  name="schedule" 
                  value="daily"
                  checked={backupSchedule === 'daily'}
                  onChange={(e) => setBackupSchedule(e.target.value)}
                  className="text-orange-500"
                />
                <span className="text-sm text-gray-700">Harian</span>
              </label>
              <label className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  name="schedule" 
                  value="weekly"
                  checked={backupSchedule === 'weekly'}
                  onChange={(e) => setBackupSchedule(e.target.value)}
                  className="text-orange-500"
                />
                <span className="text-sm text-gray-700">Mingguan</span>
              </label>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 mt-6">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Backup Sekarang
              </Button>
              <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                Atur Jadwal
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Export/Import Data */}
        <Card className="bg-white border border-gray-200">
          <CardContent className="p-4 lg:p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Export/Import Data</h3>
            <div className="space-y-3">
              <div className="flex flex-wrap gap-2">
                <Button className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm">
                  Export as CSV
                </Button>
                <Button className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm">
                  Export as JSON
                </Button>
                <Button className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm">
                  Import CSV/JSON
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Log Riwayat Backup */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-4 lg:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Log Riwayat Backup</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Tanggal</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Tipe</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Ukuran</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {backupLogs.map((log, index) => (
                  <tr key={log.id} className={`border-b border-gray-100 hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                    <td className="py-3 px-4 text-gray-700">{log.date}</td>
                    <td className="py-3 px-4 text-gray-700">{log.type}</td>
                    <td className="py-3 px-4">{getStatusBadge(log.status)}</td>
                    <td className="py-3 px-4 text-gray-700">{log.size}</td>
                    <td className="py-3 px-4">
                      <div className="flex flex-wrap gap-2">
                        <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Restore
                        </Button>
                        <Button size="sm" variant="destructive" className="bg-red-600 hover:bg-red-700">
                          <Trash2 className="h-3 w-3 mr-1" />
                          Hapus
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Storage Indicator */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-4 lg:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Indikator Penyimpanan</h3>
          <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
            {/* Pie Chart Placeholder */}
            <div className="w-32 h-32 lg:w-40 lg:h-40 rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-gray-400 flex items-center justify-center flex-shrink-0">
              <div className="w-16 h-16 lg:w-20 lg:h-20 bg-white rounded-full"></div>
            </div>
            
            {/* Storage Info */}
            <div className="space-y-2">
              <div className="text-sm text-gray-700">
                <span className="font-medium">Sisa Penyimpanan:</span> 35 GB
              </div>
              <div className="text-sm text-gray-700">
                <span className="font-medium">Digunakan:</span> 65 GB
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Backup & Restore
        </h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger 
              value="PAM" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger 
              value="SMA" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger 
              value="IBM" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>

        <TabsContent value="SMA" className="mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>

        <TabsContent value="IBM" className="mt-4 lg:mt-6">
          <BackupContent />
        </TabsContent>
      </Tabs>
    </div>
  );
}
