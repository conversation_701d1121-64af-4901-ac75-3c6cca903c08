import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../ui/tabs';
import { StatsCards } from './StatsCards';
import { VisitorChart } from './VisitorChart';
import { HeatmapCard } from './HeatmapCard';
import { RecentVisitors } from './RecentVisitors';
import { hcWithType } from 'server/dist/client';
import type { DashboardStats, ChartDataPoint, VisitorData } from 'shared/dist';

const SERVER_URL = import.meta.env.VITE_SERVER_URL || "http://localhost:3000";
const client = hcWithType(SERVER_URL);

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch stats
        const statsRes = await client['dashboard']['stats'].$get();
        if (statsRes.ok) {
          const statsData = await statsRes.json();
          setStats(statsData);
        }

        // Fetch chart data
        const chartRes = await client['dashboard']['chart'].$get();
        if (chartRes.ok) {
          const chartData = await chartRes.json();
          setChartData(chartData);
        }

        // Fetch visitors
        const visitorsRes = await client['dashboard']['visitors'].$get();
        if (visitorsRes.ok) {
          const visitorsData = await visitorsRes.json();
          setVisitors(visitorsData);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger
            value="PAM"
            className="data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            PAM
          </TabsTrigger>
          <TabsTrigger value="SMA">SMA</TabsTrigger>
          <TabsTrigger value="IBM">IBM</TabsTrigger>
        </TabsList>

        <TabsContent value="PAM" className="space-y-6 mt-6">
          {/* Stats Cards */}
          <StatsCards stats={stats} />

          {/* Chart and Heatmap */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VisitorChart data={chartData} />
            <HeatmapCard />
          </div>

          {/* Recent Visitors */}
          <RecentVisitors visitors={visitors} />
        </TabsContent>

        <TabsContent value="SMA" className="space-y-6 mt-6">
          <StatsCards stats={stats} />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VisitorChart data={chartData} />
            <HeatmapCard />
          </div>
          <RecentVisitors visitors={visitors.filter(v => v.type === 'SMA')} />
        </TabsContent>

        <TabsContent value="IBM" className="space-y-6 mt-6">
          <StatsCards stats={stats} />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VisitorChart data={chartData} />
            <HeatmapCard />
          </div>
          <RecentVisitors visitors={visitors.filter(v => v.type === 'IBM')} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
