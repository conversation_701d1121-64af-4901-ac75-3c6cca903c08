import { ReactNode } from 'react';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { Button } from '../ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import { 
  LayoutDashboard, 
  Users, 
  Settings, 
  Database, 
  FileText, 
  ChevronDown,
  Bell
} from 'lucide-react';

interface DashboardLayoutProps {
  children: ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">PAM</span>
              </div>
              <span className="text-gray-600 text-sm">PT. PAM MINERAL TBK</span>
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <Bell className="h-4 w-4" />
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-blue-600 text-white">SA</AvatarFallback>
                  </Avatar>
                  <span className="text-sm">Selamat Datang, Super Admin</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white border-r border-gray-200 min-h-[calc(100vh-73px)]">
          <nav className="p-4 space-y-2">
            <Button 
              variant="default" 
              className="w-full justify-start bg-orange-500 hover:bg-orange-600"
            >
              <LayoutDashboard className="mr-2 h-4 w-4" />
              Dashboard
            </Button>
            
            <Button variant="ghost" className="w-full justify-start text-gray-600">
              <Users className="mr-2 h-4 w-4" />
              Manajemen Pengunjung
            </Button>
            
            <Button variant="ghost" className="w-full justify-start text-gray-600">
              <Settings className="mr-2 h-4 w-4" />
              Pengaturan Sistem
            </Button>
            
            <Button variant="ghost" className="w-full justify-start text-gray-600">
              <Database className="mr-2 h-4 w-4" />
              Backup & Restore
            </Button>
            
            <Button variant="ghost" className="w-full justify-start text-gray-600">
              <FileText className="mr-2 h-4 w-4" />
              Logs
            </Button>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
