import { ReactNode, useState } from 'react';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import {
  LayoutDashboard,
  Users,
  Settings,
  Database,
  FileText,
  ChevronDown,
  Bell,
  Menu,
  X
} from 'lucide-react';

interface DashboardLayoutProps {
  children: ReactNode;
  currentPage?: string;
  onNavigate?: (page: string) => void;
}

export function DashboardLayout({ children, currentPage = 'dashboard', onNavigate }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'manajemen-pengunjung', label: 'Manajemen Pengunjung', icon: Users },
    { id: 'pengaturan-sistem', label: 'Pengaturan Sistem', icon: Settings },
    { id: 'backup-restore', label: 'Backup & Restore', icon: Database },
    { id: 'logs', label: 'Logs', icon: FileText },
  ];

  // Close sidebar when clicking outside on mobile
  const closeSidebar = () => setSidebarOpen(false);

  return (
    <div className="h-screen overflow-hidden bg-gray-50">
      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 z-50 h-16 bg-white border-b border-gray-200 px-4 lg:px-6">
        <div className="flex items-center justify-between h-full">
          {/* Mobile menu button and Logo */}
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden p-2"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>

            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                <span className="text-white font-bold text-sm">PAM</span>
              </div>
              <span className="text-gray-600 text-sm hidden sm:block truncate">PT. PAM MINERAL TBK</span>
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="p-2">
              <Bell className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2 px-2">
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-blue-600 text-white text-xs">SA</AvatarFallback>
                  </Avatar>
                  <span className="text-sm hidden md:block truncate max-w-32">Selamat Datang, Super Admin</span>
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      {/* Layout Container */}
      <div className="flex h-screen pt-16">
        {/* Mobile Sidebar Overlay - Semi-transparent */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-30 bg-black/50 lg:hidden backdrop-blur-sm"
            onClick={closeSidebar}
          />
        )}

        {/* Fixed Sidebar */}
        <aside className={`
          fixed left-0 top-16 bottom-0 z-40 w-64 bg-white border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0 lg:relative lg:top-0
        `}>
          <nav className="h-full overflow-hidden flex flex-col">
            <div className="flex-1 overflow-y-auto p-4 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;

                return (
                  <Button
                    key={item.id}
                    variant={isActive ? "default" : "ghost"}
                    className={`w-full justify-start text-sm font-medium transition-colors ${
                      isActive
                        ? "bg-orange-500 hover:bg-orange-600 text-white shadow-sm"
                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    }`}
                    onClick={() => {
                      onNavigate?.(item.id);
                      closeSidebar();
                    }}
                  >
                    <Icon className="mr-3 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">{item.label}</span>
                  </Button>
                );
              })}
            </div>
          </nav>
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden lg:ml-0">
          <div className="h-full overflow-y-auto">
            <div className="p-4 lg:p-6 min-h-full">
              <div className="max-w-full">
                {children}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
