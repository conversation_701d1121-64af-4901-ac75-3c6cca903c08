import { ReactNode, useState } from 'react';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import {
  LayoutDashboard,
  Users,
  Settings,
  Database,
  FileText,
  ChevronDown,
  ChevronRight,
  Bell,
  Menu,
  X
} from 'lucide-react';

interface DashboardLayoutProps {
  children: ReactNode;
  currentPage?: string;
  onNavigate?: (page: string) => void;
}

export function DashboardLayout({ children, currentPage = 'dashboard', onNavigate }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Check if any submenu item is active to auto-expand
  const pengaturanSistemPages = ['manajemen-pengguna', 'manajemen-konten', 'role-management'];
  const isPengaturanSistemActive = pengaturanSistemPages.includes(currentPage);
  const [pengaturanSistemOpen, setPengaturanSistemOpen] = useState(isPengaturanSistemActive);

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'manajemen-pengunjung', label: 'Manajemen Pengunjung', icon: Users },
    {
      id: 'pengaturan-sistem',
      label: 'Pengaturan Sistem',
      icon: Settings,
      hasSubmenu: true,
      submenu: [
        { id: 'manajemen-pengguna', label: 'Manajemen Pengguna' },
        { id: 'manajemen-konten', label: 'Manajemen Konten' },
        { id: 'role-management', label: 'Role Management' }
      ]
    },
    { id: 'backup-restore', label: 'Backup & Restore', icon: Database },
    { id: 'logs', label: 'Logs', icon: FileText },
  ];

  // Close sidebar when clicking outside on mobile
  const closeSidebar = () => setSidebarOpen(false);

  return (
    <div className="h-screen overflow-hidden bg-gray-50">
      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 z-50 h-16 bg-white border-b border-gray-200 px-4 lg:px-6">
        <div className="flex items-center justify-between h-full">
          {/* Mobile menu button and Logo */}
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden p-2"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>

            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center flex-shrink-0">
                <span className="text-white font-bold text-sm">PAM</span>
              </div>
              <span className="text-gray-600 text-sm hidden sm:block truncate">PT. PAM MINERAL TBK</span>
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="p-2">
              <Bell className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2 px-2">
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback className="bg-blue-600 text-white text-xs">SA</AvatarFallback>
                  </Avatar>
                  <span className="text-sm hidden md:block truncate max-w-32">Selamat Datang, Super Admin</span>
                  <ChevronDown className="h-4 w-4 flex-shrink-0" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      {/* Layout Container */}
      <div className="flex h-screen pt-16">
        {/* Mobile Sidebar Overlay - Semi-transparent */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-30 bg-black/50 lg:hidden backdrop-blur-sm"
            onClick={closeSidebar}
          />
        )}

        {/* Fixed Sidebar */}
        <aside className={`
          fixed left-0 top-16 bottom-0 z-40 w-64 bg-white border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0 lg:relative lg:top-0
        `}>
          <nav className="h-full overflow-hidden flex flex-col">
            <div className="flex-1 overflow-y-auto p-4 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;
                const isSubmenuActive = item.submenu?.some(sub => currentPage === sub.id);
                const shouldShowSubmenu = item.hasSubmenu && (pengaturanSistemOpen || isSubmenuActive);

                return (
                  <div key={item.id}>
                    <Button
                      variant={isActive ? "default" : "ghost"}
                      className={`w-full justify-start text-sm font-medium transition-colors ${
                        isActive || isSubmenuActive
                          ? "bg-orange-500 hover:bg-orange-600 text-white shadow-sm"
                          : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      }`}
                      onClick={() => {
                        if (item.hasSubmenu) {
                          setPengaturanSistemOpen(!pengaturanSistemOpen);
                        } else {
                          onNavigate?.(item.id);
                          closeSidebar();
                        }
                      }}
                    >
                      <Icon className="mr-3 h-4 w-4 flex-shrink-0" />
                      <span className="truncate flex-1 text-left">{item.label}</span>
                      {item.hasSubmenu && (
                        <ChevronRight className={`h-4 w-4 flex-shrink-0 transition-transform ${
                          shouldShowSubmenu ? 'rotate-90' : ''
                        }`} />
                      )}
                    </Button>

                    {/* Submenu */}
                    {shouldShowSubmenu && item.submenu && (
                      <div className="ml-4 mt-1 space-y-1">
                        {item.submenu.map((subItem) => {
                          const isSubActive = currentPage === subItem.id;
                          return (
                            <Button
                              key={subItem.id}
                              variant="ghost"
                              className={`w-full justify-start text-sm transition-colors ${
                                isSubActive
                                  ? "bg-orange-100 text-orange-700 hover:bg-orange-200"
                                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                              }`}
                              onClick={() => {
                                onNavigate?.(subItem.id);
                                closeSidebar();
                              }}
                            >
                              <span className="truncate pl-6">{subItem.label}</span>
                            </Button>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </nav>
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden lg:ml-0">
          <div className="h-full overflow-y-auto">
            <div className="p-4 lg:p-6 min-h-full">
              <div className="max-w-full">
                {children}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
