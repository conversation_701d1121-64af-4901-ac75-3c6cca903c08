import { useState } from 'react';
import { <PERSON>, CardContent } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { Input } from '../ui/input';
import { Download, FileText, Printer } from 'lucide-react';

interface LogEntry {
  id: string;
  no: number;
  name: string;
  email: string;
  role: string;
  ipAddress: string;
  loginDateTime: string;
}

export function Logs() {
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const [logs] = useState<LogEntry[]>([
    {
      id: '1',
      no: 1,
      name: 'admin',
      email: '<EMAIL>',
      role: 'Admin',
      ipAddress: '36.555.845',
      loginDateTime: '2025-02-21 03:24:16'
    },
    {
      id: '2',
      no: 2,
      name: 'admin',
      email: '<EMAIL>',
      role: 'Admin',
      ipAddress: '44.185.169',
      loginDateTime: '2024-12-16 06:24:16'
    },
    {
      id: '3',
      no: 3,
      name: 'admin',
      email: '<EMAIL>',
      role: 'Admin',
      ipAddress: '55.99.758',
      loginDateTime: '2025-04-21 03:24:16'
    },
    {
      id: '4',
      no: 4,
      name: 'admin',
      email: '<EMAIL>',
      role: 'Admin',
      ipAddress: '36.123.1.659',
      loginDateTime: '2025-05-10 03:24:16'
    },
    {
      id: '5',
      no: 5,
      name: 'admin',
      email: '<EMAIL>',
      role: 'Admin',
      ipAddress: '78.88.321',
      loginDateTime: '2025-01-19 03:24:16'
    }
  ]);

  const filteredLogs = logs.filter(log =>
    log.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.ipAddress.includes(searchTerm)
  );

  const totalPages = Math.ceil(filteredLogs.length / entriesPerPage);
  const startIndex = (currentPage - 1) * entriesPerPage;
  const endIndex = startIndex + entriesPerPage;
  const currentLogs = filteredLogs.slice(startIndex, endIndex);

  const LogsContent = () => (
    <div className="space-y-4 lg:space-y-6">
      {/* Controls */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        {/* Export Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm">
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm">
            <FileText className="h-3 w-3 mr-1" />
            CSV
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600 text-white text-xs sm:text-sm">
            <FileText className="h-3 w-3 mr-1" />
            PDF
          </Button>
          <Button className="bg-green-600 hover:bg-green-700 text-white text-xs sm:text-sm">
            <Printer className="h-3 w-3 mr-1" />
            Print
          </Button>
        </div>

        {/* Search */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 whitespace-nowrap">Cari</span>
          <Input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-32 sm:w-48"
          />
        </div>
      </div>

      {/* Entries per page */}
      <div className="flex items-center gap-2">
        <select
          value={entriesPerPage}
          onChange={(e) => setEntriesPerPage(Number(e.target.value))}
          className="border border-gray-300 rounded px-2 py-1 text-sm"
        >
          <option value={10}>10</option>
          <option value={25}>25</option>
          <option value={50}>50</option>
        </select>
        <span className="text-sm text-gray-600">entries per pages</span>
      </div>

      {/* Table */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">No</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Nama</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Email</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">IP Address</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Login Date Time</th>
                </tr>
              </thead>
              <tbody>
                {currentLogs.map((log, index) => (
                  <tr key={log.id} className={`border-b border-gray-100 hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                    <td className="py-3 px-4 text-gray-700">{log.no}</td>
                    <td className="py-3 px-4 text-gray-700">{log.name}</td>
                    <td className="py-3 px-4 text-gray-700">{log.email}</td>
                    <td className="py-3 px-4 text-gray-700">{log.role}</td>
                    <td className="py-3 px-4 text-gray-700">{log.ipAddress}</td>
                    <td className="py-3 px-4 text-gray-700">{log.loginDateTime}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        <div className="text-sm text-gray-600">
          Menampilkan {startIndex + 1} sampai {Math.min(endIndex, filteredLogs.length)} dari {filteredLogs.length} entri
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="text-sm"
          >
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum = i + 1;
              return (
                <Button
                  key={pageNum}
                  variant={currentPage === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(pageNum)}
                  className={`w-8 h-8 p-0 text-sm ${
                    currentPage === pageNum 
                      ? "bg-blue-600 hover:bg-blue-700 text-white" 
                      : ""
                  }`}
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="text-sm"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Logs
        </h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger 
              value="PAM" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger 
              value="SMA" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger 
              value="IBM" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="mt-4 lg:mt-6">
          <LogsContent />
        </TabsContent>

        <TabsContent value="SMA" className="mt-4 lg:mt-6">
          <LogsContent />
        </TabsContent>

        <TabsContent value="IBM" className="mt-4 lg:mt-6">
          <LogsContent />
        </TabsContent>
      </Tabs>
    </div>
  );
}
