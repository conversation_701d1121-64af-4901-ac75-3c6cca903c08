import { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { Upload } from 'lucide-react';

export function ManajemenKonten() {
  const [activeTab, setActiveTab] = useState('PAM');

  const VideoUploadSection = ({ title }: { title: string }) => (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">{title}</h3>
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardContent className="p-6 text-center">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Upload Video</h4>
            <p className="text-sm text-gray-600">Max 500MB</p>
            <Button variant="outline" className="mt-4">
              <Upload className="h-4 w-4 mr-2" />
              Upload File
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const QuizEditor = ({ title }: { title: string }) => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Quiz Question Editor</h3>
        <Button className="bg-orange-500 hover:bg-orange-600 text-white">
          Edit
        </Button>
      </div>
      <p className="text-sm text-gray-600">Drag-and-drop untuk mengurutkan pertanyaan.</p>
      
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-4 space-y-4">
          <div className="space-y-3">
            <div className="font-medium text-gray-900">
              Pertanyaan 1: Apa tujuan utama dari safety induction?
            </div>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input type="radio" name="q1" className="text-orange-500" />
                <span className="text-sm">Hijau: petunjuk/informasi</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="q1" className="text-orange-500" />
                <span className="text-sm">Kuning: peringatan</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="q1" className="text-orange-500" defaultChecked />
                <span className="text-sm font-medium">Hitam: Beracun</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="q1" className="text-orange-500" />
                <span className="text-sm">Merah: larangan</span>
              </label>
            </div>
          </div>

          <div className="space-y-3 pt-4 border-t border-gray-200">
            <div className="font-medium text-gray-900">
              Pertanyaan 2: Apakah yang harus dilakukan Visitor jika melihat percikan api/kebakaran?
            </div>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input type="radio" name="q2" className="text-orange-500" defaultChecked />
                <span className="text-sm font-medium">Melaporkan ke pendamping, dalam situasi darurat boleh melakukan pemadaman dengan pemahaman APAR</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="q2" className="text-orange-500" />
                <span className="text-sm">Langsung padamkan api menggunakan air terdekat</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="q2" className="text-orange-500" />
                <span className="text-sm">Mencari karung goni untuk memadamkan api</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="radio" name="q2" className="text-orange-500" />
                <span className="text-sm">Berteriak sekencang-kencangnya</span>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Pengaturan Sistem - Manajemen Konten
        </h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <div className="flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3 max-w-sm">
            <TabsTrigger 
              value="PAM" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              PAM
            </TabsTrigger>
            <TabsTrigger 
              value="SMA" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              SMA
            </TabsTrigger>
            <TabsTrigger 
              value="IBM" 
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white text-xs sm:text-sm"
            >
              IBM
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="PAM" className="space-y-6 mt-4 lg:mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VideoUploadSection title="Pengunjung Tipe Karyawan" />
            <VideoUploadSection title="Pengunjung Tipe Umum" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <QuizEditor title="Quiz Karyawan" />
            <QuizEditor title="Quiz Umum" />
          </div>
        </TabsContent>

        <TabsContent value="SMA" className="space-y-6 mt-4 lg:mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VideoUploadSection title="Pengunjung Tipe Karyawan" />
            <VideoUploadSection title="Pengunjung Tipe Umum" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <QuizEditor title="Quiz Karyawan" />
            <QuizEditor title="Quiz Umum" />
          </div>
        </TabsContent>

        <TabsContent value="IBM" className="space-y-6 mt-4 lg:mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VideoUploadSection title="Pengunjung Tipe Karyawan" />
            <VideoUploadSection title="Pengunjung Tipe Umum" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <QuizEditor title="Quiz Karyawan" />
            <QuizEditor title="Quiz Umum" />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
