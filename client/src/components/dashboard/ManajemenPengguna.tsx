import { useState, useEffect } from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../ui/select';
import { Search, Plus } from 'lucide-react';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  company: string;
  lastActive: string;
}

export function ManajemenPengguna() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');

  useEffect(() => {
    // Mock data
    setUsers([
      {
        id: '1',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        role: 'Super Admin',
        company: '',
        lastActive: '15 menit yang lalu'
      },
      {
        id: '2',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        role: 'Super Admin',
        company: '',
        lastActive: '1 jam yang lalu'
      },
      {
        id: '3',
        name: 'Sari Wijaya',
        email: '<EMAIL>',
        role: 'Admin - PAM',
        company: 'PAM',
        lastActive: 'Kemarin'
      },
      {
        id: '4',
        name: 'Ahmad Fauzi',
        email: '<EMAIL>',
        role: 'Admin - SMA',
        company: 'SMA',
        lastActive: 'Selasa'
      },
      {
        id: '5',
        name: 'Lestari Anggraini',
        email: '<EMAIL>',
        role: 'Admin - IBM',
        company: 'IBM',
        lastActive: '26 Juni 2025'
      }
    ]);
  }, []);

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Pengaturan Sistem - Manajemen Pengguna
        </h1>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per pages</span>
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari"
              className="w-full sm:w-64 h-10 pl-4 pr-4 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white whitespace-nowrap">
            <Plus className="h-4 w-4 mr-2" />
            Tambah User baru
          </Button>
        </div>
      </div>

      {/* Table */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-700 w-12">
                    <input type="checkbox" className="rounded" />
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Nama</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Perusahaan</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Terakhir Aktif</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user, index) => (
                  <tr key={user.id} className={`border-b border-gray-100 hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                    <td className="py-3 px-4">
                      <input type="checkbox" className="rounded" />
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </td>
                    <td className="py-3 px-4 text-gray-700">{user.role}</td>
                    <td className="py-3 px-4 text-gray-700">{user.company || '-'}</td>
                    <td className="py-3 px-4 text-gray-700">{user.lastActive}</td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                          Edit
                        </Button>
                        <Button size="sm" variant="destructive" className="bg-red-600 hover:bg-red-700">
                          Hapus
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex flex-col sm:flex-row justify-between items-center p-4 border-t border-gray-200 gap-4">
            <div className="text-sm text-gray-600">
              Menampilkan 1 sampai 5 dari 5 entri
            </div>
            <div className="flex space-x-1">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button size="sm" className="bg-blue-600 text-white">
                1
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
