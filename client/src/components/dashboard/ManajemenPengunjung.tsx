import { useState, useEffect } from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '../ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Filter, Search } from 'lucide-react';
import { hcWithType } from 'server/dist/client';
import type { DashboardStats, VisitorData } from 'shared/dist';

const SERVER_URL = import.meta.env.VITE_SERVER_URL || "http://localhost:3000";
const client = hcWithType(SERVER_URL);

export function ManajemenPengunjung() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPengunjungTerdaftar: 0,
    totalPengunjungDisetujui: 0,
    totalPengunjungDitolak: 0,
    totalPengunjungKadaluwarsa: 0,
  });
  const [visitors, setVisitors] = useState<VisitorData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch stats
        const statsRes = await client['dashboard']['stats'].$get();
        if (statsRes.ok) {
          const statsData = await statsRes.json();
          setStats(statsData);
        }

        // Fetch visitors
        const visitorsRes = await client['dashboard']['visitors'].$get();
        if (visitorsRes.ok) {
          const visitorsData = await visitorsRes.json();
          setVisitors(visitorsData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredVisitors = visitors.filter(visitor => {
    const matchesSearch = visitor.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || visitor.type === selectedType;
    return matchesSearch && matchesType;
  });

  const getVisitorsByType = (type: string) => {
    return visitors.filter(visitor => visitor.type === type);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Manajemen Pengunjung</h1>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="PAM" className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger 
            value="PAM" 
            className="data-[state=active]:bg-orange-500 data-[state=active]:text-white"
          >
            PAM
          </TabsTrigger>
          <TabsTrigger value="SMA">SMA</TabsTrigger>
          <TabsTrigger value="IBM">IBM</TabsTrigger>
        </TabsList>

        <TabsContent value="PAM" className="space-y-6 mt-6">
          <StatsCards stats={stats} />
          <VisitorGrid visitors={getVisitorsByType('PAM')} />
        </TabsContent>

        <TabsContent value="SMA" className="space-y-6 mt-6">
          <StatsCards stats={stats} />
          <VisitorGrid visitors={getVisitorsByType('SMA')} />
        </TabsContent>

        <TabsContent value="IBM" className="space-y-6 mt-6">
          <StatsCards stats={stats} />
          <VisitorGrid visitors={getVisitorsByType('IBM')} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

function StatsCards({ stats }: { stats: DashboardStats }) {
  const cards = [
    {
      title: 'Total Pengunjung Terdaftar',
      value: stats.totalPengunjungTerdaftar,
    },
    {
      title: 'Total Pengunjung Disetujui',
      value: stats.totalPengunjungDisetujui,
    },
    {
      title: 'Total Pengunjung Ditolak',
      value: stats.totalPengunjungDitolak,
    },
    {
      title: 'Total Pengunjung Kadaluwarsa',
      value: stats.totalPengunjungKadaluwarsa,
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => (
        <Card key={index} className="bg-white border border-gray-200">
          <CardContent className="p-4">
            <div className="text-xs text-gray-600 mb-1">
              {card.title}
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {card.value}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function VisitorGrid({ visitors }: { visitors: VisitorData[] }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  const filteredVisitors = visitors.filter(visitor => {
    const matchesSearch = visitor.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Tipe Pengunjung" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Semua</SelectItem>
            <SelectItem value="PAM">PAM</SelectItem>
            <SelectItem value="SMA">SMA</SelectItem>
            <SelectItem value="IBM">IBM</SelectItem>
          </SelectContent>
        </Select>
        
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search by name"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Visitor Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {filteredVisitors.map((visitor) => (
          <VisitorCard key={visitor.id} visitor={visitor} />
        ))}
      </div>
    </div>
  );
}

function VisitorCard({ visitor }: { visitor: VisitorData }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Disetujui';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Ditolak';
      default:
        return status;
    }
  };

  return (
    <Card className="bg-white border border-gray-200 hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex flex-col items-center text-center space-y-3">
          <Avatar className="w-16 h-16">
            <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${visitor.name}`} />
            <AvatarFallback className="bg-gray-200 text-gray-600">
              {visitor.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="space-y-1">
            <h3 className="font-medium text-gray-900">{visitor.name}</h3>
            <p className="text-sm text-gray-600">{visitor.company}</p>
            <p className="text-xs text-gray-500">
              Tipe Pengunjung: <span className="font-medium">{visitor.type}</span>
            </p>
            {visitor.location && (
              <p className="text-xs text-gray-500">
                Location: {visitor.location.lat.toFixed(3)}, {visitor.location.lng.toFixed(3)}
              </p>
            )}
          </div>
          
          <Button 
            size="sm" 
            className="w-full bg-orange-500 hover:bg-orange-600 text-white"
          >
            Kirim Pengingatkan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
