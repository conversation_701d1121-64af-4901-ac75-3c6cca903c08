import { useState, useEffect } from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';

interface Role {
  id: string;
  name: string;
  description: string;
  company?: string;
}

export function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([]);

  useEffect(() => {
    // Mock data
    setRoles([
      {
        id: '1',
        name: '<PERSON><PERSON><PERSON>',
        description: 'Full akses dashboard, bisa membuat role dan user',
        company: ''
      },
      {
        id: '2',
        name: 'Super Admin',
        description: 'Full akses dashboard, bisa membuat role dan user',
        company: ''
      },
      {
        id: '3',
        name: 'Admin - PAM',
        description: 'Full akses dashboard perusahaan PAM',
        company: 'PAM'
      },
      {
        id: '4',
        name: 'Admin - SMA',
        description: 'Full akses dashboard perusahaan SMA',
        company: 'SMA'
      },
      {
        id: '5',
        name: 'Admin - IBM',
        description: 'Full akses dashboard perusahaan IBM',
        company: 'IBM'
      }
    ]);
  }, []);

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full">
      {/* Page Header */}
      <div className="flex-shrink-0">
        <h1 className="text-xl lg:text-2xl font-bold text-gray-900 truncate">
          Pengaturan Sistem - Role Management
        </h1>
      </div>

      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-lg font-medium text-gray-900">Edit Role Access</h2>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white whitespace-nowrap">
          <Plus className="h-4 w-4 mr-2" />
          Tambah Role
        </Button>
      </div>

      {/* Table */}
      <Card className="bg-white border border-gray-200">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Role</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Perusahaan</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {roles.map((role, index) => (
                  <tr key={role.id} className={`border-b border-gray-100 hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900 mb-1">{role.name}</div>
                        <div className="text-sm text-gray-600">{role.description}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-gray-700">
                        {role.company || '-'}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex space-x-2">
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                          Edit
                        </Button>
                        <Button size="sm" variant="destructive" className="bg-red-600 hover:bg-red-700">
                          Hapus
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
