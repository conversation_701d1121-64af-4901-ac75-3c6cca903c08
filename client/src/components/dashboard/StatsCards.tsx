import { Card, CardContent } from '../ui/card';
import type { DashboardStats } from 'shared/dist';

interface StatsCardsProps {
  stats: DashboardStats;
}

export function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      title: 'Total Pengunjung Terdaftar',
      value: stats.totalPengunjungTerdaftar,
      bgColor: 'bg-white'
    },
    {
      title: 'Total Pengunjung Disetujui',
      value: stats.totalPengunjungDisetujui,
      bgColor: 'bg-white'
    },
    {
      title: 'Total Pengunjung Ditolak',
      value: stats.totalPengunjungDitolak,
      bgColor: 'bg-white'
    },
    {
      title: 'Total Pengunjung Kadaluwarsa',
      value: stats.totalPengunjungKadaluwarsa,
      bgColor: 'bg-white'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {cards.map((card, index) => (
        <Card key={index} className={`${card.bgColor} border border-gray-200`}>
          <CardContent className="p-6">
            <div className="text-sm text-gray-600 mb-2">
              {card.title}
            </div>
            <div className="text-3xl font-bold text-gray-900">
              {card.value}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
