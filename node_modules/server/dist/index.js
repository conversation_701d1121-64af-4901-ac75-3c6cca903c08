import { Hono } from "hono";
import { cors } from "hono/cors";
export const app = new Hono()
    .use(cors())
    .get("/", (c) => {
    return c.text("Hello Hono!");
})
    .get("/hello", async (c) => {
    const data = {
        message: "Hello BHVR!",
        success: true,
    };
    return c.json(data, { status: 200 });
})
    .get("/dashboard/stats", async (c) => {
    const stats = {
        totalPengunjungTerdaftar: 17,
        totalPengunjungDisetujui: 10,
        totalPengunjungDitolak: 2,
        totalPengunjungKadaluwarsa: 5,
    };
    return c.json(stats, { status: 200 });
})
    .get("/dashboard/chart", async (c) => {
    const chartData = [
        { day: "Senin", value: 40 },
        { day: "Selasa", value: 35 },
        { day: "Rabu", value: 45 },
        { day: "Kamis", value: 95 },
        { day: "Jumat", value: 80 },
        { day: "Sabtu", value: 65 },
    ];
    return c.json(chartData, { status: 200 });
})
    .get("/dashboard/chart/weekly", async (c) => {
    const weeklyData = [
        { day: "Minggu 1", value: 280 },
        { day: "Minggu 2", value: 320 },
        { day: "Minggu 3", value: 290 },
        { day: "Minggu 4", value: 410 },
    ];
    return c.json(weeklyData, { status: 200 });
})
    .get("/dashboard/visitors", async (c) => {
    const visitors = [
        {
            id: "1",
            name: "Ahmad Rizki",
            company: "PT. Teknologi Maju",
            type: "PAM",
            status: "approved",
            date: "2024-01-15",
            location: { lat: -6.2088, lng: 106.8456 }
        },
        {
            id: "2",
            name: "Siti Nurhaliza",
            company: "CV. Berkah Jaya",
            type: "SMA",
            status: "pending",
            date: "2024-01-16"
        },
        {
            id: "3",
            name: "Budi Santoso",
            company: "PT. Industri Besar",
            type: "IBM",
            status: "approved",
            date: "2024-01-17",
            location: { lat: -6.1751, lng: 106.8650 }
        }
    ];
    return c.json(visitors, { status: 200 });
});
export default app;
