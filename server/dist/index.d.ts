export declare const app: import("hono/hono-base").HonoBase<{}, {
    "/": {
        $get: {
            input: {};
            output: "Hello Hono!";
            outputFormat: "text";
            status: import("hono/utils/http-status").ContentfulStatusCode;
        };
    };
} & {
    "/hello": {
        $get: {
            input: {};
            output: {
                message: string;
                success: true;
            };
            outputFormat: "json";
            status: 200;
        };
    };
} & {
    "/dashboard/stats": {
        $get: {
            input: {};
            output: {
                totalPengunjungTerdaftar: number;
                totalPengunjungDisetujui: number;
                totalPengunjungDitolak: number;
                totalPengunjungKadaluwarsa: number;
            };
            outputFormat: "json";
            status: 200;
        };
    };
} & {
    "/dashboard/chart": {
        $get: {
            input: {};
            output: {
                day: string;
                value: number;
            }[];
            outputFormat: "json";
            status: 200;
        };
    };
} & {
    "/dashboard/chart/weekly": {
        $get: {
            input: {};
            output: {
                day: string;
                value: number;
            }[];
            outputFormat: "json";
            status: 200;
        };
    };
} & {
    "/dashboard/visitors": {
        $get: {
            input: {};
            output: {
                id: string;
                name: string;
                company: string;
                type: "PAM" | "SMA" | "IBM";
                status: "approved" | "pending" | "rejected";
                date: string;
                location?: {
                    lat: number;
                    lng: number;
                } | undefined;
            }[];
            outputFormat: "json";
            status: 200;
        };
    };
}, "/">;
export default app;
